//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Liam")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("现代化串口通讯功能库，支持跨平台串口设备发现、连接管理、数据收发、热插拔检测和自动重连等功能")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0+bcdbdcbde0cbb845d161fe20b59916ba6f989d2d")]
[assembly: System.Reflection.AssemblyProductAttribute("Liam.SerialPort")]
[assembly: System.Reflection.AssemblyTitleAttribute("Liam.SerialPort")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://gitee.com/liam-gitee/liam")]

// 由 MSBuild WriteCodeFragment 类生成。

